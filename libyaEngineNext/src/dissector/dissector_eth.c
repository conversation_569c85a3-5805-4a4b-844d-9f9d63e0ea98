#include "yaEngineNext/nxt_engine.h"
#include <yaProtoRecord/precord.h>
#include <yaFtypes/fvalue.h>
#include <yaBasicUtils/macro.h>
#include <stddef.h>

#define PROTO_NAME "eth"

static
int ether_dissect(nxt_engine_t *engine, nxt_session_t *session _U_, nxt_mbuf_t *mbuf)
{
    precord_t *precord = nxt_engine_pktzone_get_precord(engine);
    precord_layer_put_new_layer_cache(precord, PROTO_NAME);

    precord_put(precord, "dst",  bytes,    nxt_mbuf_get_raw(mbuf, 0), 6);
    precord_put(precord, "src",  bytes,    nxt_mbuf_get_raw(mbuf, 6), 6);

    uint16_t type_length = nxt_mbuf_get_uint16_ntoh(mbuf, 12);
    precord_put(precord, "type", uinteger, type_length);

    // Distinguish between IEEE 802.3 and Ethernet II formats
    if (type_length <= 1500) {
        // IEEE 802.3 format: type_length field contains length
        // Next layer should be LLC (802.2)
        precord_put(precord, "frame_format", string, "IEEE 802.3");
        precord_put(precord, "length", uinteger, type_length);

        // For IEEE 802.3, handoff to LLC
        // Use a special key to indicate LLC should be used
        nxt_handoff_set_key_of_number(engine, 0x8000); // Special key for LLC
    } else if (type_length >= 1536) {
        // Ethernet II format: type_length field contains EtherType
        precord_put(precord, "frame_format", string, "Ethernet II");
        precord_put(precord, "ethertype", uinteger, type_length);

        // For Ethernet II, use the EtherType for handoff
        nxt_handoff_set_key_of_number(engine, type_length);
    } else {
        // Invalid range (1501-1535), should not occur in practice
        precord_put(precord, "frame_format", string, "Invalid");
        printf("ETH: Invalid type/length field value: %d\n", type_length);
        return -1;
    }

    return 14;
}

static
int ether_schema_reg(nxt_engine_t *engine _U_, pschema_db_t *db)
{
     /* 注册 schema */
     pschema_t* pschema = pschema_register_proto(db, PSCHEMA_TEMPLATE_NONE, PROTO_NAME, "ethernet");
     pschema_register_field(pschema, "dst",  YA_FT_ETHER,  "dst mac address");
     pschema_register_field(pschema, "src",  YA_FT_ETHER,  "src mac address");
     pschema_register_field_ex(pschema, "type", YA_FT_UINT16, "type/length field", YA_DISPLAY_BASE_HEX);
     pschema_register_field(pschema, "frame_format", YA_FT_STRING, "frame format (IEEE 802.3 or Ethernet II)");
     pschema_register_field(pschema, "length", YA_FT_UINT16, "frame length (IEEE 802.3)");
     pschema_register_field_ex(pschema, "ethertype", YA_FT_UINT16, "ether type (Ethernet II)", YA_DISPLAY_BASE_HEX);

     return 0;
}

static nxt_dissector_def_t gDissectorDef =
{
    .name         = "eth",
    .schemaRegFun = ether_schema_reg,
    .dissectFun   = ether_dissect,
    .handoff      = NXT_HANDOFF_DEFAULT,
    .mountAt      = {
        NXT_MNT_END,
    },
};

NXT_DISSECTOR_INIT(eth)
{
    nxt_dissector_register(&gDissectorDef);
}
